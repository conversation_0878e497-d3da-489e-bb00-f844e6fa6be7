
import React from 'react';
import { Database, Server, FileText, BarChart3, Info, Wifi, WifiOff } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useQuery } from '../context/QueryContext';
import ResultsTable from './ResultsTable';

const DestinationPanel = () => {
  const {
    destinationServer,
    setDestinationServer,
    destinationDatabase,
    setDestinationDatabase,
    destinationQuery,
    setDestinationQuery,
    destinationResults,
    isDestinationLoading,
    comparison,
    availableServers,
    destinationDatabases,
    isDestinationDatabasesLoading,
  } = useQuery();

  const isConnected = destinationServer !== '';

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-green-50 to-emerald-50 shadow-lg shadow-black/50 rounded-lg">
      <div className="p-4 sm:p-6 lg:p-8 border-b-2 border-green-200 bg-gradient-to-r from-green-100 to-emerald-100 rounded-t-lg">
        <h2 className="text-base sm:text-lg lg:text-xl font-bold text-green-900 mb-4 sm:mb-6 flex items-center gap-3">
          <div className="p-2 bg-green-600 rounded-full">
            <Database className="w-3 h-3 sm:w-4 sm:h-4 lg:w-6 lg:h-6 text-white" />
          </div>
          Destination Activities
          <div className="flex items-center gap-2 ml-auto">
            {isConnected ? (
              <>
                <Wifi className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-600">Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-600">Disconnected</span>
              </>
            )}
          </div>
        </h2>
        
        <div className="space-y-6">
          {/* Desktop: Grid layout, Mobile/Tablet: Stacked layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Label htmlFor="destination-server" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                      <Server className="w-4 h-4" />
                      Select Destination Server
                      <Info className="w-3 h-3 text-green-600 cursor-help" />
                    </Label>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Choose the server where your destination data is located</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Select value={destinationServer} onValueChange={setDestinationServer}>
                <SelectTrigger className="border-2 border-green-300 focus:border-green-500 bg-white shadow-md">
                  <SelectValue placeholder="Choose server..." />
                </SelectTrigger>
                <SelectContent>
                  {availableServers.map((server) => (
                    <SelectItem key={server.id} value={server.id}>
                      <div className="flex items-center gap-2">
                        <Server className="w-4 h-4 text-green-600" />
                        {server.alias}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Label htmlFor="destination-database" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      Select Database
                      <Info className="w-3 h-3 text-green-600 cursor-help" />
                    </Label>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Select the specific database to query from</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Select value={destinationDatabase} onValueChange={setDestinationDatabase} disabled={!destinationServer || isDestinationDatabasesLoading}>
                <SelectTrigger className="border-2 border-green-300 focus:border-green-500 bg-white shadow-md">
                  <SelectValue placeholder={isDestinationDatabasesLoading ? "Loading databases..." : "Choose database..."} />
                </SelectTrigger>
                <SelectContent>
                  {destinationDatabases.map((database) => (
                    <SelectItem key={database.name} value={database.name}>
                      <div className="flex items-center gap-2">
                        <Database className="w-4 h-4 text-green-600" />
                        <div className="flex flex-col">
                          <span>{database.name}</span>
                          {database.size && (
                            <span className="text-xs text-gray-500">{database.size}</span>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Label htmlFor="destination-query" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    SQL Query
                    <Info className="w-3 h-3 text-green-600 cursor-help" />
                  </Label>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Enter your SQL query to execute on the destination database</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Textarea
              id="destination-query"
              value={destinationQuery}
              onChange={(e) => setDestinationQuery(e.target.value)}
              placeholder="SELECT * FROM table_name WHERE condition..."
              className="mt-2 h-32 sm:h-40 font-mono text-sm resize-none border-2 border-green-300 focus:border-green-500 bg-white shadow-md"
            />
          </div>
        </div>
      </div>
      
      <div className="flex-1 p-4 sm:p-6 lg:p-8 overflow-auto bg-white rounded-b-lg">
        <div className="flex items-center gap-2 mb-4">
          <BarChart3 className="w-5 h-5 text-green-600" />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <h3 className="text-base sm:text-lg font-semibold text-green-900 flex items-center gap-2">
                  Query Results
                  <Info className="w-3 h-3 text-green-600 cursor-help" />
                </h3>
              </TooltipTrigger>
              <TooltipContent>
                <p>Results from executing your destination query</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <ResultsTable 
          results={destinationResults} 
          loading={isDestinationLoading} 
          comparison={comparison}
          type="destination"
        />
      </div>
    </div>
  );
};

export default DestinationPanel;
