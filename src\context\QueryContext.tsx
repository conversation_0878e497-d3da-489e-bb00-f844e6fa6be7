
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { databaseService, DatabaseInfo } from '../services/databaseService';

interface QueryResult {
  data: Record<string, any>[];
  totalRows: number;
}

interface QueryContextType {
  sourceServer: string;
  setSourceServer: (server: string) => void;
  sourceDatabase: string;
  setSourceDatabase: (database: string) => void;
  sourceQuery: string;
  setSourceQuery: (query: string) => void;
  sourceResults: QueryResult | null;
  setSourceResults: (results: QueryResult | null) => void;
  destinationServer: string;
  setDestinationServer: (server: string) => void;
  destinationDatabase: string;
  setDestinationDatabase: (database: string) => void;
  destinationQuery: string;
  setDestinationQuery: (query: string) => void;
  destinationResults: QueryResult | null;
  setDestinationResults: (results: QueryResult | null) => void;
  isSourceLoading: boolean;
  setIsSourceLoading: (loading: boolean) => void;
  isDestinationLoading: boolean;
  setIsDestinationLoading: (loading: boolean) => void;
  comparison: 'none' | 'completed';
  setComparison: (comparison: 'none' | 'completed') => void;
  // New properties for database service integration
  availableServers: Array<{ id: string; alias: string }>;
  sourceDatabases: DatabaseInfo[];
  destinationDatabases: DatabaseInfo[];
  isSourceDatabasesLoading: boolean;
  isDestinationDatabasesLoading: boolean;
  loadDatabases: (serverId: string, type: 'source' | 'destination') => Promise<void>;
}

const QueryContext = createContext<QueryContextType | undefined>(undefined);

export const useQuery = () => {
  const context = useContext(QueryContext);
  if (!context) {
    throw new Error('useQuery must be used within a QueryProvider');
  }
  return context;
};

export const QueryProvider = ({ children }: { children: ReactNode }) => {
  const [sourceServer, setSourceServer] = useState('');
  const [sourceDatabase, setSourceDatabase] = useState('');
  const [sourceQuery, setSourceQuery] = useState('');
  const [sourceResults, setSourceResults] = useState<QueryResult | null>(null);
  const [destinationServer, setDestinationServer] = useState('');
  const [destinationDatabase, setDestinationDatabase] = useState('');
  const [destinationQuery, setDestinationQuery] = useState('');
  const [destinationResults, setDestinationResults] = useState<QueryResult | null>(null);
  const [isSourceLoading, setIsSourceLoading] = useState(false);
  const [isDestinationLoading, setIsDestinationLoading] = useState(false);
  const [comparison, setComparison] = useState<'none' | 'completed'>('none');

  // New state for database service integration
  const [availableServers, setAvailableServers] = useState<Array<{ id: string; alias: string }>>([]);
  const [sourceDatabases, setSourceDatabases] = useState<DatabaseInfo[]>([]);
  const [destinationDatabases, setDestinationDatabases] = useState<DatabaseInfo[]>([]);
  const [isSourceDatabasesLoading, setIsSourceDatabasesLoading] = useState(false);
  const [isDestinationDatabasesLoading, setIsDestinationDatabasesLoading] = useState(false);

  // Load available servers on component mount
  useEffect(() => {
    const servers = databaseService.getAvailableServers();
    setAvailableServers(servers);
  }, []);

  // Reset database selection when server changes
  useEffect(() => {
    if (sourceServer) {
      setSourceDatabase('');
      loadDatabases(sourceServer, 'source');
    } else {
      setSourceDatabases([]);
    }
  }, [sourceServer]);

  useEffect(() => {
    if (destinationServer) {
      setDestinationDatabase('');
      loadDatabases(destinationServer, 'destination');
    } else {
      setDestinationDatabases([]);
    }
  }, [destinationServer]);

  const loadDatabases = async (serverId: string, type: 'source' | 'destination') => {
    const setLoading = type === 'source' ? setIsSourceDatabasesLoading : setIsDestinationDatabasesLoading;
    const setDatabases = type === 'source' ? setSourceDatabases : setDestinationDatabases;

    setLoading(true);
    try {
      const databases = await databaseService.getDatabases(serverId);
      setDatabases(databases);
    } catch (error) {
      console.error(`Failed to load databases for ${type}:`, error);
      setDatabases([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <QueryContext.Provider
      value={{
        sourceServer,
        setSourceServer,
        sourceDatabase,
        setSourceDatabase,
        sourceQuery,
        setSourceQuery,
        sourceResults,
        setSourceResults,
        destinationServer,
        setDestinationServer,
        destinationDatabase,
        setDestinationDatabase,
        destinationQuery,
        setDestinationQuery,
        destinationResults,
        setDestinationResults,
        isSourceLoading,
        setIsSourceLoading,
        isDestinationLoading,
        setIsDestinationLoading,
        comparison,
        setComparison,
        availableServers,
        sourceDatabases,
        destinationDatabases,
        isSourceDatabasesLoading,
        isDestinationDatabasesLoading,
        loadDatabases,
      }}
    >
      {children}
    </QueryContext.Provider>
  );
};
