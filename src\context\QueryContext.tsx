
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface QueryResult {
  data: Record<string, any>[];
  totalRows: number;
}

interface QueryContextType {
  sourceServer: string;
  setSourceServer: (server: string) => void;
  sourceDatabase: string;
  setSourceDatabase: (database: string) => void;
  sourceQuery: string;
  setSourceQuery: (query: string) => void;
  sourceResults: QueryResult | null;
  setSourceResults: (results: QueryResult | null) => void;
  destinationServer: string;
  setDestinationServer: (server: string) => void;
  destinationDatabase: string;
  setDestinationDatabase: (database: string) => void;
  destinationQuery: string;
  setDestinationQuery: (query: string) => void;
  destinationResults: QueryResult | null;
  setDestinationResults: (results: QueryResult | null) => void;
  isSourceLoading: boolean;
  setIsSourceLoading: (loading: boolean) => void;
  isDestinationLoading: boolean;
  setIsDestinationLoading: (loading: boolean) => void;
  comparison: 'none' | 'completed';
  setComparison: (comparison: 'none' | 'completed') => void;
}

const QueryContext = createContext<QueryContextType | undefined>(undefined);

export const useQuery = () => {
  const context = useContext(QueryContext);
  if (!context) {
    throw new Error('useQuery must be used within a QueryProvider');
  }
  return context;
};

export const QueryProvider = ({ children }: { children: ReactNode }) => {
  const [sourceServer, setSourceServer] = useState('');
  const [sourceDatabase, setSourceDatabase] = useState('');
  const [sourceQuery, setSourceQuery] = useState('');
  const [sourceResults, setSourceResults] = useState<QueryResult | null>(null);
  const [destinationServer, setDestinationServer] = useState('');
  const [destinationDatabase, setDestinationDatabase] = useState('');
  const [destinationQuery, setDestinationQuery] = useState('');
  const [destinationResults, setDestinationResults] = useState<QueryResult | null>(null);
  const [isSourceLoading, setIsSourceLoading] = useState(false);
  const [isDestinationLoading, setIsDestinationLoading] = useState(false);
  const [comparison, setComparison] = useState<'none' | 'completed'>('none');

  return (
    <QueryContext.Provider
      value={{
        sourceServer,
        setSourceServer,
        sourceDatabase,
        setSourceDatabase,
        sourceQuery,
        setSourceQuery,
        sourceResults,
        setSourceResults,
        destinationServer,
        setDestinationServer,
        destinationDatabase,
        setDestinationDatabase,
        destinationQuery,
        setDestinationQuery,
        destinationResults,
        setDestinationResults,
        isSourceLoading,
        setIsSourceLoading,
        isDestinationLoading,
        setIsDestinationLoading,
        comparison,
        setComparison,
      }}
    >
      {children}
    </QueryContext.Provider>
  );
};
