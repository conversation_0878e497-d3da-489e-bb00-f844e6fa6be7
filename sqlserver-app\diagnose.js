const sql = require('mssql');

// Test different SQL Server connection configurations
async function testConnections() {
  console.log('🔍 Diagnosing SQL Server connections...\n');

  const testConfigs = [
    {
      name: 'Local SQL Server (Windows Auth)',
      config: {
        server: 'localhost',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          integratedSecurity: true,
          connectTimeout: 5000,
        }
      }
    },
    {
      name: 'Local SQL Server Express (Windows Auth)',
      config: {
        server: 'localhost\\SQLEXPRESS',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          integratedSecurity: true,
          connectTimeout: 5000,
        }
      }
    },
    {
      name: 'Local SQL Server Express (Named Pipes)',
      config: {
        server: '.\\SQLEXPRESS',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          integratedSecurity: true,
          connectTimeout: 5000,
        }
      }
    },
    {
      name: 'Computer Name (Windows Auth)',
      config: {
        server: 'DESKTOP-1TR3ETK',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          integratedSecurity: true,
          connectTimeout: 5000,
        }
      }
    },
    {
      name: 'Computer Name with SQL Auth',
      config: {
        server: 'DESKTOP-1TR3ETK',
        user: 'DESKTOP-1TR3ETK\\Sohaib Majeed',
        password: 'CodxLoop',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          connectTimeout: 5000,
        }
      }
    },
    {
      name: 'LocalDB',
      config: {
        server: '(localdb)\\MSSQLLocalDB',
        options: {
          encrypt: false,
          trustServerCertificate: true,
          integratedSecurity: true,
          connectTimeout: 5000,
        }
      }
    }
  ];

  for (const test of testConfigs) {
    try {
      console.log(`🔄 Testing: ${test.name}`);
      console.log(`   Server: ${test.config.server}`);
      
      const pool = new sql.ConnectionPool(test.config);
      await pool.connect();
      
      // Test a simple query
      const result = await pool.request().query('SELECT @@VERSION as version, @@SERVERNAME as server_name, DB_NAME() as database_name');
      
      console.log(`✅ SUCCESS! Connected to: ${test.name}`);
      console.log(`   Server Name: ${result.recordset[0].server_name}`);
      console.log(`   Database: ${result.recordset[0].database_name}`);
      console.log(`   Version: ${result.recordset[0].version.substring(0, 100)}...`);
      
      // Get list of databases
      const dbResult = await pool.request().query('SELECT name FROM sys.databases WHERE state = 0 ORDER BY name');
      console.log(`   Available Databases: ${dbResult.recordset.map(db => db.name).join(', ')}`);
      
      await pool.close();
      console.log('');
      
      // If we found a working connection, we can stop here
      return test.config;
      
    } catch (error) {
      console.log(`❌ FAILED: ${test.name}`);
      console.log(`   Error: ${error.message}`);
      console.log('');
    }
  }
  
  console.log('❌ No working SQL Server connections found.');
  console.log('\n📋 Troubleshooting steps:');
  console.log('1. Make sure SQL Server is installed and running');
  console.log('2. Check SQL Server Configuration Manager');
  console.log('3. Enable TCP/IP protocol');
  console.log('4. Start SQL Server service');
  console.log('5. Check Windows Firewall settings');
  
  return null;
}

// Run the diagnosis
testConnections()
  .then((workingConfig) => {
    if (workingConfig) {
      console.log('🎉 Found working configuration!');
      console.log('You can use this configuration in your application.');
    }
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Diagnosis failed:', error);
    process.exit(1);
  });
