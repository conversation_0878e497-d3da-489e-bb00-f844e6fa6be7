import React from 'react';
import { Loader2, Trash2, <PERSON>, GitCompare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useQuery } from '../context/QueryContext';
import { useToast } from '@/hooks/use-toast';
import { databaseService } from '../services/databaseService';

const ControlPanel = () => {
  const {
    sourceServer,
    sourceDatabase,
    sourceQuery,
    destinationServer,
    destinationDatabase,
    destinationQuery,
    setSourceResults,
    setDestinationResults,
    isSourceLoading,
    setIsSourceLoading,
    isDestinationLoading,
    setIsDestinationLoading,
    sourceResults,
    destinationResults,
    setComparison,
    setSourceQuery,
    setDestinationQuery,
  } = useQuery();

  const { toast } = useToast();

  const generateMockData = (query: string, rowCount: number = 5) => {
    const columns = ['id', 'name', 'email', 'created_date', 'status'];
    const data = [];
    
    for (let i = 1; i <= rowCount; i++) {
      data.push({
        id: i,
        name: `User ${i}`,
        email: `user${i}@example.com`,
        created_date: `2024-01-${String(i).padStart(2, '0')}`,
        status: i % 2 === 0 ? 'Active' : 'Inactive'
      });
    }
    
    return { data, totalRows: rowCount };
  };

  const executeSourceQuery = async () => {
    if (!sourceServer || !sourceDatabase || !sourceQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select server, database, and enter a query for source.",
        variant: "destructive",
      });
      return;
    }

    setIsSourceLoading(true);

    try {
      const results = await databaseService.executeQuery(sourceServer, sourceDatabase, sourceQuery);
      setSourceResults(results);
      setComparison('none');

      const serverConfig = databaseService.getServerConfig(sourceServer);
      const serverAlias = serverConfig?.alias || sourceServer;

      toast({
        title: "Source Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${serverAlias}/${sourceDatabase}`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute source query",
        variant: "destructive",
      });
    } finally {
      setIsSourceLoading(false);
    }
  };

  const executeDestinationQuery = async () => {
    if (!destinationServer || !destinationDatabase || !destinationQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select server, database, and enter a query for destination.",
        variant: "destructive",
      });
      return;
    }

    setIsDestinationLoading(true);

    try {
      const results = await databaseService.executeQuery(destinationServer, destinationDatabase, destinationQuery);
      setDestinationResults(results);
      setComparison('none');

      const serverConfig = databaseService.getServerConfig(destinationServer);
      const serverAlias = serverConfig?.alias || destinationServer;

      toast({
        title: "Destination Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${serverAlias}/${destinationDatabase}`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute destination query",
        variant: "destructive",
      });
    } finally {
      setIsDestinationLoading(false);
    }
  };

  const compareResults = () => {
    if (!sourceResults || !destinationResults) {
      toast({
        title: "Cannot Compare",
        description: "Please execute both source and destination queries first.",
        variant: "destructive",
      });
      return;
    }

    setComparison('completed');
    
    toast({
      title: "Comparison Completed",
      description: "Results have been compared and highlighted.",
    });
  };

  const clearQueries = () => {
    setSourceQuery('');
    setDestinationQuery('');
    setSourceResults(null);
    setDestinationResults(null);
    setComparison('none');
    
    toast({
      title: "Queries Cleared",
      description: "All queries and results have been cleared.",
    });
  };

  return (
    <div className="h-full flex flex-col items-center justify-center bg-gradient-to-b from-purple-50 to-pink-50 shadow-lg shadow-black/50 rounded-lg p-4 sm:p-8">      
      <div className="space-y-4 sm:space-y-6 lg:space-y-8 w-full max-w-xs">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeSourceQuery}
                disabled={isSourceLoading}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105"
              >
                {isSourceLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Source Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the source database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeDestinationQuery}
                disabled={isDestinationLoading}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-green-500 via-green-600 to-green-700 hover:from-green-600 hover:via-green-700 hover:to-green-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105"
              >
                {isDestinationLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Destination Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the destination database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={compareResults}
                disabled={!sourceResults || !destinationResults}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-purple-500 via-purple-600 to-pink-600 hover:from-purple-600 hover:via-purple-700 hover:to-pink-700 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                <GitCompare className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Compare Results
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Compare the results from both queries and highlight differences</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={clearQueries}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105"
              >
                <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Clear Queries
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Clear all queries and results to start fresh</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

export default ControlPanel;
