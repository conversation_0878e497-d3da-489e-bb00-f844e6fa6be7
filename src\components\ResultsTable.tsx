
import React from 'react';
import { Loader2, FileText } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useQuery } from '../context/QueryContext';

interface ResultsTableProps {
  results: { data: Record<string, any>[]; totalRows: number } | null;
  loading: boolean;
  comparison: 'none' | 'completed';
  type: 'source' | 'destination';
}

const ResultsTable = ({ results, loading, comparison, type }: ResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  const getComparisonClass = (rowIndex: number) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) {
      return 'bg-red-50 border-red-200';
    }

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];

  return (
    <div className="space-y-4">
      <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-sm font-medium text-slate-700">
            Total Rows: {results.totalRows}
          </span>
        </div>
      </div>

      <div className="border border-slate-200 rounded-lg overflow-hidden">
        <div className="overflow-auto max-h-96">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50">
                {columns.map((column) => (
                  <TableHead key={column} className="font-semibold text-slate-700 border-b border-slate-200">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {results.data.map((row, index) => (
                <TableRow 
                  key={index} 
                  className={`hover:bg-slate-50 transition-colors ${getComparisonClass(index)}`}
                >
                  {columns.map((column) => (
                    <TableCell key={column} className="border-b border-slate-100 text-sm">
                      {row[column]}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {comparison === 'completed' && (
        <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-slate-700">Matched Rows</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-slate-700">Mismatched Rows</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsTable;
